# 小红书自动化工具使用指南

## 📋 项目简介

这是一个完整的小红书内容自动化工具，包含Chrome插件和自动化脚本，可以实现：

1. **内容采集** - 从小红书页面采集笔记内容
2. **AI改写** - 使用大模型API改写内容
3. **批量导出** - 导出为Excel文件
4. **自动发布** - 自动填写创作中心并保存草稿

## 🚀 快速开始

### 第一步：安装Chrome插件

1. 打开Chrome浏览器，进入扩展程序管理页面：
   ```
   chrome://extensions/
   ```

2. 开启"开发者模式"

3. 点击"加载已解压的扩展程序"

4. 选择 `chrome-extension` 文件夹

5. 插件安装完成后，会在浏览器工具栏显示

### 第二步：配置AI API

1. 点击插件图标，打开设置面板

2. 填入您的AI API配置：
   - **API Key**: 您的OpenAI或其他兼容API的密钥
   - **API地址**: 默认为 `https://api.openai.com/v1/chat/completions`

3. 点击"保存配置"

### 第三步：采集内容

1. 在小红书网站浏览笔记

2. 点击插件图标，选择采集方式：
   - **采集当前笔记** - 采集当前正在查看的笔记
   - **批量采集页面** - 采集页面上所有可见的笔记卡片

3. 点击"AI改写内容"进行内容改写

4. 点击"导出Excel"保存数据

### 第四步：准备自动化环境

1. 创建Chrome用户配置文件：
   ```bash
   # 在Chrome地址栏输入：
   chrome://settings/people
   
   # 点击"添加用户"，创建名为"AutomationBot"的配置文件
   ```

2. 在AutomationBot配置文件中登录小红书账号

3. 安装Python依赖：
   ```bash
   cd automation-script
   pip install -r requirements.txt
   ```

### 第五步：运行自动化脚本

1. 将插件导出的Excel文件放在 `automation-script` 目录下

2. 运行自动化脚本：
   ```bash
   python main.py
   ```

3. 按提示输入Excel文件路径

4. 确认后脚本会自动：
   - 打开Chrome浏览器
   - 导航到创作中心
   - 批量填写笔记内容
   - 保存为草稿

## 📁 文件结构

```
xiaohongshu-automation/
├── chrome-extension/          # Chrome插件
│   ├── manifest.json         # 插件配置
│   ├── popup.html            # 弹窗界面
│   ├── popup.js              # 弹窗逻辑
│   ├── content.js            # 内容脚本
│   └── background.js         # 后台脚本
├── automation-script/         # 自动化脚本
│   ├── main.py               # 主程序
│   ├── browser_controller.py # 浏览器控制
│   └── requirements.txt      # Python依赖
├── shared/                   # 共享配置
│   └── config.json          # 配置文件
└── README.md                # 使用说明
```

## ⚙️ 配置选项

### Chrome插件配置

在插件弹窗中可以配置：
- **API Key**: AI服务的API密钥
- **API地址**: AI服务的接口地址

### 自动化脚本配置

编辑 `shared/config.json` 文件：

```json
{
  "browser_profile": "AutomationBot",     // Chrome配置文件名
  "headless": false,                      // 是否无头模式
  "delay_between_posts": 10,              // 发布间隔（秒）
  "max_retries": 3,                       // 最大重试次数
  "screenshots_dir": "screenshots",       // 截图保存目录
  "excel_file": "notes_data.xlsx",        // 默认Excel文件
  "login_timeout": 60                     // 登录超时时间（秒）
}
```

## 🛠️ 高级功能

### 自定义CSS选择器

如果小红书页面结构发生变化，可以在 `config.json` 中更新选择器：

```json
{
  "selectors": {
    "title_input": ["//input[contains(@placeholder, '标题')]"],
    "content_input": ["//textarea[contains(@placeholder, '内容')]"],
    "save_draft_button": ["//button[contains(text(), '保存草稿')]"]
  }
}
```

### 批量处理模式

脚本支持批量处理多个Excel文件：

```bash
python main.py --batch --input-dir ./excel_files
```

### 定时发布

可以配置定时任务：

```bash
# 每天早上9点执行
0 9 * * * /usr/bin/python3 /path/to/main.py
```

## 🔧 故障排除

### 常见问题

1. **插件无法识别页面内容**
   - 确保在小红书的笔记详情页使用
   - 检查页面是否完全加载

2. **AI改写失败**
   - 检查API Key是否正确
   - 确认API地址是否可访问
   - 检查网络连接

3. **自动化脚本无法启动**
   - 确认Chrome浏览器已安装
   - 检查Python依赖是否完整安装
   - 确认ChromeDriver版本匹配

4. **无法登录小红书**
   - 在AutomationBot配置文件中手动登录
   - 检查账号是否被限制

### 日志查看

插件日志：
```javascript
// 在Chrome开发者工具Console中查看
console.log('插件日志');
```

脚本日志：
```bash
# 脚本运行时会输出详细日志
python main.py > automation.log 2>&1
```

## 🔒 安全注意事项

1. **API Key保护**
   - 不要在公共场所分享API Key
   - 定期更换API Key

2. **账号安全**
   - 不要频繁发布避免触发限制
   - 合理设置发布间隔

3. **数据备份**
   - 定期备份采集的数据
   - 导出Excel文件保存在安全位置

## 📊 性能优化

1. **提升采集效率**
   - 使用批量采集模式
   - 合理设置AI改写批次

2. **优化发布速度**
   - 调整发布间隔
   - 使用无头模式（测试通过后）

3. **减少资源占用**
   - 关闭不必要的Chrome标签
   - 定期清理截图文件

## 📈 扩展功能

### 支持更多平台

可以扩展支持其他社交平台：
- 微博
- 抖音
- 知乎

### 高级AI功能

- 智能标签生成
- 内容质量评估
- 发布时间优化

## 🤝 贡献指南

欢迎提交Issue和Pull Request：

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题请创建Issue或发送邮件。

---

⚡ **开始使用，让内容创作更高效！**