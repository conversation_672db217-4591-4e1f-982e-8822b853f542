// content.js - 内容脚本，注入到小红书页面

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
} else {
    initialize();
}

function initialize() {
    console.log('小红书内容采集器已加载');
    
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'extractNote') {
            const noteData = extractNoteContent();
            sendResponse({success: true, data: noteData});
        } else if (request.action === 'batchExtract') {
            const notes = batchExtractNotes();
            sendResponse({success: true, data: notes});
        }
    });
    
    // 添加采集按钮到页面（可选）
    addCollectionButton();
}

function extractNoteContent() {
    const result = {
        title: '',
        content: '',
        tags: [],
        images: [],
        url: window.location.href,
        author: '',
        likes: 0,
        comments: 0
    };

    try {
        // 提取标题 - 多种选择器适配不同页面结构
        const titleSelectors = [
            '.note-detail .title',
            '[data-testid="note-title"]',
            '.note-item .title',
            '.note-scroller .title',
            'h1.title',
            '.note-content .title'
        ];
        
        for (const selector of titleSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                result.title = element.textContent.trim();
                break;
            }
        }

        // 提取内容
        const contentSelectors = [
            '.note-detail .content',
            '[data-testid="note-content"]',
            '.note-item .content',
            '.note-scroller .content',
            '.note-content .desc',
            '.note-text'
        ];
        
        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                result.content = element.textContent.trim();
                break;
            }
        }

        // 提取标签
        const tagSelectors = [
            '.tag',
            '[data-testid="note-tag"]',
            '.note-tag',
            '.tags .tag-item',
            '.hash-tag'
        ];
        
        tagSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(tag => {
                const tagText = tag.textContent.trim().replace(/^#/, '');
                if (tagText && !result.tags.includes(tagText)) {
                    result.tags.push(tagText);
                }
            });
        });

        // 提取图片
        const imageSelectors = [
            '.note-detail img',
            '[data-testid="note-image"]',
            '.note-item img',
            '.note-scroller img',
            '.carousel-item img'
        ];
        
        imageSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(img => {
                if (img.src && !result.images.includes(img.src)) {
                    result.images.push(img.src);
                }
            });
        });

        // 提取作者信息
        const authorSelectors = [
            '.author-name',
            '.user-name',
            '[data-testid="author-name"]',
            '.note-author .name'
        ];
        
        for (const selector of authorSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                result.author = element.textContent.trim();
                break;
            }
        }

        // 提取点赞数
        const likeSelectors = [
            '.like-count',
            '[data-testid="like-count"]',
            '.engagement-count .like'
        ];
        
        for (const selector of likeSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const likeText = element.textContent.trim();
                const likeMatch = likeText.match(/\d+/);
                if (likeMatch) {
                    result.likes = parseInt(likeMatch[0]);
                    break;
                }
            }
        }

        // 提取评论数
        const commentSelectors = [
            '.comment-count',
            '[data-testid="comment-count"]',
            '.engagement-count .comment'
        ];
        
        for (const selector of commentSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const commentText = element.textContent.trim();
                const commentMatch = commentText.match(/\d+/);
                if (commentMatch) {
                    result.comments = parseInt(commentMatch[0]);
                    break;
                }
            }
        }

        console.log('提取到的笔记内容:', result);
        return result;
        
    } catch (error) {
        console.error('提取笔记内容失败:', error);
        return result;
    }
}

function batchExtractNotes() {
    const notes = [];
    
    try {
        // 查找所有笔记卡片
        const noteSelectors = [
            '.note-item',
            '[data-testid="note-card"]',
            '.feed-item',
            '.note-card',
            '.waterfall-item'
        ];
        
        let noteElements = [];
        for (const selector of noteSelectors) {
            noteElements = document.querySelectorAll(selector);
            if (noteElements.length > 0) break;
        }
        
        console.log(`找到 ${noteElements.length} 个笔记卡片`);
        
        noteElements.forEach((noteElement, index) => {
            const noteData = {
                title: '',
                content: '',
                tags: [],
                images: [],
                url: window.location.href,
                author: '',
                position: index + 1
            };

            // 从卡片中提取标题
            const titleEl = noteElement.querySelector('.title') || 
                           noteElement.querySelector('.note-title') ||
                           noteElement.querySelector('h3');
            if (titleEl) {
                noteData.title = titleEl.textContent.trim();
            }

            // 从卡片中提取内容预览
            const contentEl = noteElement.querySelector('.content') ||
                             noteElement.querySelector('.desc') ||
                             noteElement.querySelector('.note-desc') ||
                             noteElement.querySelector('p');
            if (contentEl) {
                noteData.content = contentEl.textContent.trim();
            }

            // 从卡片中提取标签
            const tagEls = noteElement.querySelectorAll('.tag') ||
                          noteElement.querySelectorAll('.hash-tag');
            tagEls.forEach(tag => {
                const tagText = tag.textContent.trim().replace(/^#/, '');
                if (tagText && !noteData.tags.includes(tagText)) {
                    noteData.tags.push(tagText);
                }
            });

            // 从卡片中提取图片
            const imgEls = noteElement.querySelectorAll('img');
            imgEls.forEach(img => {
                if (img.src && !noteData.images.includes(img.src)) {
                    noteData.images.push(img.src);
                }
            });

            // 从卡片中提取作者
            const authorEl = noteElement.querySelector('.author') ||
                            noteElement.querySelector('.user-name') ||
                            noteElement.querySelector('.username');
            if (authorEl) {
                noteData.author = authorEl.textContent.trim();
            }

            // 只保存有内容的笔记
            if (noteData.title || noteData.content) {
                notes.push(noteData);
            }
        });

        console.log(`批量提取完成，共提取 ${notes.length} 篇笔记`);
        return notes;
        
    } catch (error) {
        console.error('批量提取失败:', error);
        return notes;
    }
}

function addCollectionButton() {
    // 创建浮动采集按钮
    const collectBtn = document.createElement('div');
    collectBtn.id = 'xiaohongshu-collect-btn';
    collectBtn.innerHTML = '📎 采集';
    collectBtn.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: #ff2442;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
        z-index: 9999;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        user-select: none;
    `;
    
    collectBtn.addEventListener('mouseenter', () => {
        collectBtn.style.transform = 'scale(1.1)';
    });
    
    collectBtn.addEventListener('mouseleave', () => {
        collectBtn.style.transform = 'scale(1)';
    });
    
    collectBtn.addEventListener('click', async () => {
        try {
            collectBtn.innerHTML = '⏳';
            const noteData = extractNoteContent();
            
            // 发送到background script保存
            const response = await chrome.runtime.sendMessage({
                action: 'saveNote',
                data: noteData
            });
            
            if (response.success) {
                collectBtn.innerHTML = '✅';
                showNotification('采集成功！');
            } else {
                collectBtn.innerHTML = '❌';
                showNotification('采集失败，请重试');
            }
            
            setTimeout(() => {
                collectBtn.innerHTML = '📎 采集';
            }, 2000);
            
        } catch (error) {
            console.error('采集错误:', error);
            collectBtn.innerHTML = '❌';
            showNotification('采集失败，请重试');
            setTimeout(() => {
                collectBtn.innerHTML = '📎 采集';
            }, 2000);
        }
    });
    
    document.body.appendChild(collectBtn);
}

function showNotification(message) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #333;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        z-index: 10000;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 100);
    
    // 3秒后移除
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 监听页面变化，适应SPA应用
let currentUrl = window.location.href;
const observer = new MutationObserver(() => {
    if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        console.log('页面已切换:', currentUrl);
        
        // 页面切换后重新初始化
        setTimeout(() => {
            if (!document.getElementById('xiaohongshu-collect-btn')) {
                addCollectionButton();
            }
        }, 1000);
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});