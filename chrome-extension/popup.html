<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 15px;
        }
        .button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .primary-btn {
            background-color: #ff2442;
            color: white;
        }
        .secondary-btn {
            background-color: #f0f0f0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 12px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .config-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .input-group {
            margin-bottom: 10px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 12px;
        }
        .collected-count {
            text-align: center;
            font-weight: bold;
            color: #ff2442;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>小红书内容采集器</h3>
    </div>
    
    <div class="section">
        <div class="collected-count">
            已采集: <span id="collectedCount">0</span> 篇笔记
        </div>
    </div>
    
    <div class="section">
        <button class="button primary-btn" id="extractBtn">采集当前笔记</button>
        <button class="button secondary-btn" id="batchExtractBtn">批量采集页面</button>
    </div>
    
    <div class="section">
        <button class="button primary-btn" id="rewriteBtn">AI改写内容</button>
        <button class="button secondary-btn" id="exportBtn">导出Excel</button>
    </div>
    
    <div class="section">
        <button class="button secondary-btn" id="clearBtn">清空数据</button>
    </div>
    
    <div id="status" class="status info" style="display: none;">
        准备就绪
    </div>
    
    <div class="config-section">
        <h4>配置</h4>
        <div class="input-group">
            <label for="apiKey">AI API Key:</label>
            <input type="password" id="apiKey" placeholder="输入你的API Key">
        </div>
        <div class="input-group">
            <label for="apiUrl">API地址:</label>
            <input type="text" id="apiUrl" placeholder="https://api.deepseek.com/v1/chat/completions">
        </div>
        <button class="button secondary-btn" id="saveConfigBtn">保存配置</button>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>