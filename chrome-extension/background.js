// background.js - 后台脚本

// 安装时初始化
chrome.runtime.onInstalled.addListener(() => {
    console.log('小红书内容采集器已安装');
});

// 处理来自content script和popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'saveNote') {
        saveNoteToStorage(request.data)
            .then(() => sendResponse({success: true}))
            .catch(error => {
                console.error('保存笔记失败:', error);
                sendResponse({success: false, error: error.message});
            });
        return true; // 保持消息通道开放
    }
    
    if (request.action === 'getAllNotes') {
        getAllNotesFromStorage()
            .then(notes => sendResponse({success: true, notes: notes}))
            .catch(error => {
                console.error('获取笔记失败:', error);
                sendResponse({success: false, error: error.message});
            });
        return true;
    }
    
    if (request.action === 'clearAllNotes') {
        clearAllNotesFromStorage()
            .then(() => sendResponse({success: true}))
            .catch(error => {
                console.error('清空笔记失败:', error);
                sendResponse({success: false, error: error.message});
            });
        return true;
    }
});

// 存储笔记到本地
async function saveNoteToStorage(noteData) {
    try {
        const result = await chrome.storage.local.get(['notes']);
        const notes = result.notes || [];
        
        // 添加时间戳和唯一ID
        const newNote = {
            id: Date.now().toString(),
            timestamp: new Date().toISOString(),
            ...noteData
        };
        
        notes.push(newNote);
        
        await chrome.storage.local.set({notes: notes});
        console.log('笔记已保存:', newNote);
        
        // 发送通知
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icon48.png',
            title: '采集成功',
            message: `已采集笔记: ${noteData.title || '无标题'}`
        });
        
    } catch (error) {
        console.error('保存笔记失败:', error);
        throw error;
    }
}

// 获取所有存储的笔记
async function getAllNotesFromStorage() {
    try {
        const result = await chrome.storage.local.get(['notes']);
        return result.notes || [];
    } catch (error) {
        console.error('获取笔记失败:', error);
        throw error;
    }
}

// 清空所有笔记
async function clearAllNotesFromStorage() {
    try {
        await chrome.storage.local.remove(['notes']);
        console.log('所有笔记已清空');
    } catch (error) {
        console.error('清空笔记失败:', error);
        throw error;
    }
}

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local' && changes.notes) {
        console.log('笔记存储已更新:', changes.notes.newValue?.length || 0, '篇');
    }
});

// 处理快捷键
chrome.commands.onCommand.addListener(async (command) => {
    if (command === 'collect_current_note') {
        try {
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            
            if (tab.url.includes('xiaohongshu.com')) {
                const result = await chrome.scripting.executeScript({
                    target: {tabId: tab.id},
                    function: extractNoteContent
                });
                
                if (result[0]?.result) {
                    await saveNoteToStorage(result[0].result);
                    chrome.notifications.create({
                        type: 'basic',
                        iconUrl: 'icon48.png',
                        title: '快捷采集成功',
                        message: `已采集笔记: ${result[0].result.title || '无标题'}`
                    });
                }
            } else {
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'icon48.png',
                    title: '采集失败',
                    message: '请在小红书页面使用此功能'
                });
            }
        } catch (error) {
            console.error('快捷采集失败:', error);
        }
    }
});

// 定期清理过期数据（可选）
setInterval(async () => {
    try {
        const result = await chrome.storage.local.get(['notes']);
        const notes = result.notes || [];
        
        // 删除30天前的笔记
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const filteredNotes = notes.filter(note => {
            const noteDate = new Date(note.timestamp);
            return noteDate > thirtyDaysAgo;
        });
        
        if (filteredNotes.length !== notes.length) {
            await chrome.storage.local.set({notes: filteredNotes});
            console.log(`已清理 ${notes.length - filteredNotes.length} 条过期笔记`);
        }
    } catch (error) {
        console.error('清理过期数据失败:', error);
    }
}, 24 * 60 * 60 * 1000); // 每24小时执行一次

// 提取笔记内容的函数（注入到页面）
function extractNoteContent() {
    const result = {
        title: '',
        content: '',
        tags: [],
        images: [],
        url: window.location.href,
        author: '',
        likes: 0,
        comments: 0
    };

    try {
        // 提取标题
        const titleSelectors = [
            '.note-detail .title',
            '[data-testid="note-title"]',
            '.note-item .title',
            '.note-scroller .title',
            'h1.title',
            '.note-content .title'
        ];
        
        for (const selector of titleSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                result.title = element.textContent.trim();
                break;
            }
        }

        // 提取内容
        const contentSelectors = [
            '.note-detail .content',
            '[data-testid="note-content"]',
            '.note-item .content',
            '.note-scroller .content',
            '.note-content .desc',
            '.note-text'
        ];
        
        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                result.content = element.textContent.trim();
                break;
            }
        }

        // 提取标签
        const tagElements = document.querySelectorAll('.tag, [data-testid="note-tag"], .note-tag, .hash-tag');
        tagElements.forEach(tag => {
            const tagText = tag.textContent.trim().replace(/^#/, '');
            if (tagText && !result.tags.includes(tagText)) {
                result.tags.push(tagText);
            }
        });

        // 提取图片
        const imageElements = document.querySelectorAll('.note-detail img, [data-testid="note-image"], .note-item img, .carousel-item img');
        imageElements.forEach(img => {
            if (img.src && !result.images.includes(img.src)) {
                result.images.push(img.src);
            }
        });

        return result;
        
    } catch (error) {
        console.error('提取笔记内容失败:', error);
        return result;
    }
}