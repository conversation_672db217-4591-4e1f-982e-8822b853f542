// popup.js - 插件弹窗控制脚本

class PopupController {
    constructor() {
        this.initializeElements();
        this.loadConfig();
        this.updateCollectedCount();
        this.bindEvents();
    }

    initializeElements() {
        this.extractBtn = document.getElementById('extractBtn');
        this.batchExtractBtn = document.getElementById('batchExtractBtn');
        this.rewriteBtn = document.getElementById('rewriteBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.saveConfigBtn = document.getElementById('saveConfigBtn');
        this.statusDiv = document.getElementById('status');
        this.collectedCountSpan = document.getElementById('collectedCount');
        this.apiKeyInput = document.getElementById('apiKey');
        this.apiUrlInput = document.getElementById('apiUrl');
    }

    bindEvents() {
        this.extractBtn.addEventListener('click', () => this.extractCurrentNote());
        this.batchExtractBtn.addEventListener('click', () => this.batchExtract());
        this.rewriteBtn.addEventListener('click', () => this.rewriteContent());
        this.exportBtn.addEventListener('click', () => this.exportToExcel());
        this.clearBtn.addEventListener('click', () => this.clearData());
        this.saveConfigBtn.addEventListener('click', () => this.saveConfig());
    }

    showStatus(message, type = 'info') {
        this.statusDiv.textContent = message;
        this.statusDiv.className = `status ${type}`;
        this.statusDiv.style.display = 'block';
        setTimeout(() => {
            this.statusDiv.style.display = 'none';
        }, 3000);
    }

    async extractCurrentNote() {
        try {
            this.showStatus('正在采集当前笔记...', 'info');
            
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            
            if (!tab.url.includes('xiaohongshu.com')) {
                this.showStatus('请在小红书页面使用此功能', 'error');
                return;
            }

            const result = await chrome.scripting.executeScript({
                target: {tabId: tab.id},
                function: extractNoteContent
            });

            if (result[0]?.result) {
                await this.saveNoteData(result[0].result);
                this.showStatus('采集成功！', 'success');
                this.updateCollectedCount();
            } else {
                this.showStatus('采集失败，请确保在笔记详情页', 'error');
            }
        } catch (error) {
            console.error('采集错误:', error);
            this.showStatus('采集失败，请重试', 'error');
        }
    }

    async batchExtract() {
        try {
            this.showStatus('正在批量采集...', 'info');
            
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            
            const result = await chrome.scripting.executeScript({
                target: {tabId: tab.id},
                function: batchExtractNotes
            });

            if (result[0]?.result && result[0].result.length > 0) {
                for (const note of result[0].result) {
                    await this.saveNoteData(note);
                }
                this.showStatus(`批量采集成功！采集了${result[0].result.length}篇笔记`, 'success');
                this.updateCollectedCount();
            } else {
                this.showStatus('未发现可采集的笔记', 'error');
            }
        } catch (error) {
            console.error('批量采集错误:', error);
            this.showStatus('批量采集失败，请重试', 'error');
        }
    }

    async rewriteContent() {
        try {
            this.showStatus('正在AI改写内容...', 'info');
            
            const config = await this.getConfig();
            if (!config.apiKey || !config.apiUrl) {
                this.showStatus('请先配置API Key和地址', 'error');
                return;
            }

            const data = await this.getStoredData();
            if (!data.notes || data.notes.length === 0) {
                this.showStatus('没有需要改写的内容', 'error');
                return;
            }

            let rewrittenCount = 0;
            for (const note of data.notes) {
                if (!note.rewrittenContent) {
                    try {
                        const rewritten = await this.rewriteWithAI(note, config);
                        note.rewrittenContent = rewritten.content;
                        note.rewrittenTitle = rewritten.title;
                        note.rewrittenTags = rewritten.tags;
                        rewrittenCount++;
                    } catch (error) {
                        console.error('改写失败:', error);
                    }
                }
            }

            await this.saveAllData(data);
            this.showStatus(`AI改写完成！改写了${rewrittenCount}篇笔记`, 'success');
        } catch (error) {
            console.error('改写错误:', error);
            this.showStatus('改写失败，请重试', 'error');
        }
    }

    async rewriteWithAI(note, config) {
        const prompt = `请改写以下小红书笔记内容，保持原意但换个表达方式：

原标题：${note.title}
原内容：${note.content}
原标签：${note.tags.join(', ')}

请以JSON格式返回改写后的内容：
{
  "title": "改写后的标题",
  "content": "改写后的内容",
  "tags": ["改写后的标签1", "改写后的标签2"]
}`;

        const response = await fetch(config.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify({
                model: 'deepseek-chat',
                messages: [
                    {role: 'system', content: '你是一个专业的内容创作助手，擅长改写小红书笔记内容。请保持原意的基础上，让内容更加生动有趣，符合小红书的风格。'},
                    {role: 'user', content: prompt}
                ],
                max_tokens: 1000,
                temperature: 0.7
            })
        });

        const result = await response.json();
        const content = result.choices[0].message.content;
        
        try {
            return JSON.parse(content);
        } catch (error) {
            return {
                title: note.title,
                content: content,
                tags: note.tags
            };
        }
    }

    async exportToExcel() {
        try {
            this.showStatus('正在导出Excel...', 'info');
            
            const data = await this.getStoredData();
            if (!data.notes || data.notes.length === 0) {
                this.showStatus('没有可导出的数据', 'error');
                return;
            }

            const csvContent = this.convertToCSV(data.notes);
            this.downloadCSV(csvContent, 'xiaohongshu_notes.csv');
            this.showStatus('导出成功！', 'success');
        } catch (error) {
            console.error('导出错误:', error);
            this.showStatus('导出失败，请重试', 'error');
        }
    }

    convertToCSV(notes) {
        const headers = ['标题', '内容', '标签', '图片数量', '改写标题', '改写内容', '改写标签', '采集时间'];
        const rows = notes.map(note => [
            note.title || '',
            note.content || '',
            note.tags.join('; ') || '',
            note.images.length || 0,
            note.rewrittenTitle || '',
            note.rewrittenContent || '',
            note.rewrittenTags ? note.rewrittenTags.join('; ') : '',
            note.timestamp || ''
        ]);

        return [headers, ...rows].map(row => 
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }

    downloadCSV(content, filename) {
        const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    async clearData() {
        if (confirm('确定要清空所有数据吗？')) {
            await chrome.storage.local.clear();
            this.updateCollectedCount();
            this.showStatus('数据已清空', 'success');
        }
    }

    async saveConfig() {
        const config = {
            apiKey: this.apiKeyInput.value,
            apiUrl: this.apiUrlInput.value
        };
        await chrome.storage.local.set({config});
        this.showStatus('配置已保存', 'success');
    }

    async loadConfig() {
        const result = await chrome.storage.local.get(['config']);
        if (result.config) {
            this.apiKeyInput.value = result.config.apiKey || '';
            this.apiUrlInput.value = result.config.apiUrl || 'https://api.deepseek.com/v1/chat/completions';
        }
    }

    async getConfig() {
        const result = await chrome.storage.local.get(['config']);
        return result.config || {};
    }

    async saveNoteData(noteData) {
        const data = await this.getStoredData();
        data.notes.push({
            ...noteData,
            timestamp: new Date().toISOString()
        });
        await chrome.storage.local.set(data);
    }

    async getStoredData() {
        const result = await chrome.storage.local.get(['notes']);
        return {notes: result.notes || []};
    }

    async saveAllData(data) {
        await chrome.storage.local.set(data);
    }

    async updateCollectedCount() {
        const data = await this.getStoredData();
        this.collectedCountSpan.textContent = data.notes.length;
    }
}

// 注入到页面的内容提取函数
function extractNoteContent() {
    const result = {
        title: '',
        content: '',
        tags: [],
        images: [],
        url: window.location.href
    };

    // 提取标题
    const titleElement = document.querySelector('.note-item .title') || 
                        document.querySelector('[data-testid="note-title"]') ||
                        document.querySelector('.note-detail .title');
    if (titleElement) {
        result.title = titleElement.textContent.trim();
    }

    // 提取内容
    const contentElement = document.querySelector('.note-item .content') ||
                          document.querySelector('[data-testid="note-content"]') ||
                          document.querySelector('.note-detail .content');
    if (contentElement) {
        result.content = contentElement.textContent.trim();
    }

    // 提取标签
    const tagElements = document.querySelectorAll('.tag') || 
                       document.querySelectorAll('[data-testid="note-tag"]');
    tagElements.forEach(tag => {
        const tagText = tag.textContent.trim().replace('#', '');
        if (tagText) {
            result.tags.push(tagText);
        }
    });

    // 提取图片
    const imageElements = document.querySelectorAll('.note-item img') ||
                         document.querySelectorAll('[data-testid="note-image"]');
    imageElements.forEach(img => {
        if (img.src) {
            result.images.push(img.src);
        }
    });

    return result;
}

function batchExtractNotes() {
    const notes = [];
    const noteElements = document.querySelectorAll('.note-item') || 
                        document.querySelectorAll('[data-testid="note-card"]');
    
    noteElements.forEach(noteElement => {
        const noteData = {
            title: '',
            content: '',
            tags: [],
            images: [],
            url: window.location.href
        };

        // 从每个笔记卡片中提取信息
        const titleEl = noteElement.querySelector('.title');
        if (titleEl) noteData.title = titleEl.textContent.trim();

        const contentEl = noteElement.querySelector('.content');
        if (contentEl) noteData.content = contentEl.textContent.trim();

        const tagEls = noteElement.querySelectorAll('.tag');
        tagEls.forEach(tag => {
            const tagText = tag.textContent.trim().replace('#', '');
            if (tagText) noteData.tags.push(tagText);
        });

        const imgEls = noteElement.querySelectorAll('img');
        imgEls.forEach(img => {
            if (img.src) noteData.images.push(img.src);
        });

        if (noteData.title || noteData.content) {
            notes.push(noteData);
        }
    });

    return notes;
}

// 初始化弹窗控制器
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});