# 小红书内容自动化工具

## 项目概述

这是一个自动化工具，用于从小红书采集内容并自动发布到创作中心。

### 功能特性

1. **Chrome插件** - 识别和改写小红书笔记
   - 自动识别小红书笔记的标题、内容、标签、配图
   - 集成AI大模型进行内容改写
   - 批量处理和Excel导出功能

2. **自动化脚本** - 自动发布到创作中心
   - 自动打开指定Chrome浏览器
   - 读取Excel文件并自动填写创作中心
   - 保存草稿并关闭浏览器

## 项目结构

```
xiaohongshu-automation/
├── chrome-extension/          # Chrome插件
│   ├── manifest.json
│   ├── popup.html
│   ├── popup.js
│   ├── content.js
│   └── background.js
├── automation-script/         # 自动化脚本
│   ├── main.py
│   ├── browser_controller.py
│   └── requirements.txt
├── shared/                    # 共享资源
│   ├── config.json
│   └── templates/
└── README.md
```

## 使用方法

1. 安装Chrome插件
2. 配置大模型API
3. 在小红书页面使用插件采集内容
4. 运行自动化脚本发布内容

## 依赖环境

- Python 3.8+
- Chrome浏览器
- Selenium WebDriver