# DeepSeek API 测试脚本

import os
import sys
sys.path.append('automation-script')

from ai_rewriter import AIRewriter

def test_deepseek_api():
    """测试DeepSeek API连接和改写功能"""
    print("🧪 开始测试DeepSeek API...")
    
    try:
        # 创建AI改写器实例
        rewriter = AIRewriter()
        
        # 1. 测试连接
        print("\n📡 测试API连接...")
        if not rewriter.test_connection():
            print("❌ API连接失败")
            return False
        
        # 2. 测试改写功能
        print("\n📝 测试内容改写功能...")
        test_note = {
            'title': '今日份的美食分享',
            'content': '今天做了一道红烧肉，味道很不错。用了五花肉、生抽、老抽、冰糖和料酒。先炒糖色，再下肉炒香，最后焖煮30分钟。',
            'tags': ['美食', '红烧肉', '家常菜', '下饭菜']
        }
        
        result = rewriter.rewrite_note(
            title=test_note['title'],
            content=test_note['content'],
            tags=test_note['tags']
        )
        
        print("✅ 改写测试完成!")
        print(f"\n📋 原内容:")
        print(f"标题: {test_note['title']}")
        print(f"内容: {test_note['content']}")
        print(f"标签: {', '.join(test_note['tags'])}")
        
        print(f"\n🆕 改写结果:")
        print(f"标题: {result['title']}")
        print(f"内容: {result['content']}")
        print(f"标签: {', '.join(result['tags'])}")
        
        # 3. 测试批量改写
        print("\n📚 测试批量改写功能...")
        test_notes = [
            {
                'title': '今天的穿搭分享',
                'content': '今天穿了一件白色T恤配牛仔裤，简单清爽的日常穿搭。',
                'tags': ['穿搭', 'OOTD', '日常']
            },
            {
                'title': '护肤心得',
                'content': '最近在用一款新的面霜，质地很滋润，适合干性皮肤。',
                'tags': ['护肤', '面霜', '干皮']
            }
        ]
        
        batch_results = rewriter.batch_rewrite(test_notes)
        print(f"✅ 批量改写完成，处理了 {len(batch_results)} 条笔记")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_deepseek_api()
    
    if success:
        print("\n🎉 所有测试通过！DeepSeek API集成成功")
        print("\n📝 使用说明:")
        print("1. Chrome插件中配置:")
        print("   - API Key: [请设置你的DeepSeek API Key]")
        print("   - API地址: https://api.deepseek.com/v1/chat/completions")
        print("\n2. 自动化脚本会自动使用环境变量中的配置")
        print("3. 如果Excel中没有改写内容，脚本会自动调用AI改写")
    else:
        print("\n❌ 测试失败，请检查配置")
        print("1. 确认API Key是否正确")
        print("2. 检查网络连接")
        print("3. 确认.env文件配置")