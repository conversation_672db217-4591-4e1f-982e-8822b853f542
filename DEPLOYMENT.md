# 小红书自动化工具 - 部署指南

## 🚀 系统要求

### 硬件要求
- **内存**: 最低4GB，推荐8GB以上
- **存储**: 至少1GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Chrome浏览器**: 版本90以上
- **Python**: 3.8及以上版本
- **Node.js**: 16.0及以上版本（可选，用于开发）

## 💻 环境配置

### 1. Python环境配置

#### Windows
```cmd
# 下载并安装Python 3.8+
# 从 https://www.python.org/downloads/ 下载

# 验证安装
python --version
pip --version

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
cd automation-script
pip install -r requirements.txt
```

#### macOS
```bash
# 使用Homebrew安装Python
brew install python3

# 验证安装
python3 --version
pip3 --version

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
cd automation-script
pip install -r requirements.txt
```

#### Linux (Ubuntu/Debian)
```bash
# 更新包管理器
sudo apt update

# 安装Python和pip
sudo apt install python3 python3-pip python3-venv

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
cd automation-script
pip install -r requirements.txt
```

### 2. Chrome浏览器配置

#### 创建专用用户配置文件

1. **方法一：通过Chrome界面**
   ```
   1. 打开Chrome浏览器
   2. 点击右上角头像
   3. 选择"添加用户"
   4. 创建名为"AutomationBot"的配置文件
   5. 在该配置文件中登录小红书账号
   ```

2. **方法二：通过命令行**
   ```bash
   # Windows
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --user-data-dir="C:\ChromeProfiles\AutomationBot"
   
   # macOS
   /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --user-data-dir="~/ChromeProfiles/AutomationBot"
   
   # Linux
   google-chrome --user-data-dir="~/ChromeProfiles/AutomationBot"
   ```

#### 配置Chrome扩展

1. 启用开发者模式：
   ```
   chrome://extensions/
   ```

2. 加载插件：
   ```
   点击"加载已解压的扩展程序"
   选择chrome-extension文件夹
   ```

## 🔧 部署步骤

### 1. 下载项目文件

```bash
# 如果使用Git
git clone https://github.com/your-repo/xiaohongshu-automation.git
cd xiaohongshu-automation

# 或者直接下载ZIP文件并解压
```

### 2. 安装Chrome插件

1. 打开Chrome浏览器
2. 进入扩展程序页面：`chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 文件夹
6. 确认插件已成功加载

### 3. 配置Python环境

```bash
# 进入项目目录
cd automation-script

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import selenium; print('Selenium安装成功')"
```

### 4. 配置文件设置

#### 创建环境变量文件

```bash
# 在automation-script目录下创建.env文件
echo "OPENAI_API_KEY=your_api_key_here" > .env
echo "OPENAI_API_BASE=https://api.openai.com/v1" >> .env
```

#### 编辑配置文件

```json
// shared/config.json
{
  "browser_profile": "AutomationBot",
  "headless": false,
  "delay_between_posts": 10,
  "max_retries": 3,
  "screenshots_dir": "screenshots",
  "excel_file": "notes_data.xlsx",
  "login_timeout": 60
}
```

### 5. 权限配置

#### Windows
```cmd
# 确保Chrome和Python有足够权限
# 可能需要以管理员身份运行
```

#### macOS
```bash
# 给予Python网络访问权限
# 在System Preferences > Security & Privacy > Privacy > Full Disk Access
# 添加Terminal或Python可执行文件

# 如果使用自动化，可能需要
sudo xattr -r -d com.apple.quarantine /path/to/chrome
```

#### Linux
```bash
# 安装必要的系统依赖
sudo apt-get update
sudo apt-get install -y wget unzip curl

# 设置Chrome权限
sudo chmod +x /usr/bin/google-chrome
```

## 🌐 网络配置

### 代理设置

如果需要通过代理访问：

```python
# 在browser_controller.py中添加代理配置
chrome_options.add_argument('--proxy-server=http://proxy.example.com:8080')
```

### 防火墙配置

确保以下端口可以访问：
- **443**: HTTPS访问小红书
- **80**: HTTP访问
- **API端口**: 如果使用自定义API服务

## 📊 性能优化

### 1. 系统优化

```bash
# 增加Chrome进程优先级
# Windows
wmic process where name="chrome.exe" CALL setpriority "high priority"

# Linux
sudo renice -10 -p $(pgrep chrome)
```

### 2. 内存优化

```python
# 在config.json中设置
{
  "chrome_options": {
    "memory_pressure_off": true,
    "max_old_space_size": 4096
  }
}
```

### 3. 并发优化

```python
# 可以配置多个浏览器实例
{
  "concurrent_browsers": 2,
  "batch_size": 10
}
```

## 🔒 安全配置

### 1. API密钥保护

```bash
# 使用环境变量而不是硬编码
export OPENAI_API_KEY="your_secret_key"
export XIAOHONGSHU_USERNAME="your_username"
```

### 2. 用户数据保护

```python
# 加密存储用户数据
import json
from cryptography.fernet import Fernet

def encrypt_data(data):
    key = Fernet.generate_key()
    f = Fernet(key)
    return f.encrypt(json.dumps(data).encode())
```

### 3. 访问控制

```python
# 限制脚本访问权限
import os
os.umask(0o077)  # 只允许用户访问
```

## 📝 日志配置

### 1. 日志级别设置

```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('automation.log'),
        logging.StreamHandler()
    ]
)
```

### 2. 日志轮转

```python
from logging.handlers import RotatingFileHandler

handler = RotatingFileHandler(
    'automation.log', 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
```

## 🚀 生产环境部署

### 1. 使用Docker部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装Chrome
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    xvfb

# 安装Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb https://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable

# 设置工作目录
WORKDIR /app

# 复制文件
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 启动脚本
CMD ["python", "main.py"]
```

### 2. 使用systemd服务

```ini
# /etc/systemd/system/xiaohongshu-automation.service
[Unit]
Description=Xiaohongshu Automation Service
After=network.target

[Service]
Type=simple
User=automation
WorkingDirectory=/home/<USER>/xiaohongshu-automation/automation-script
ExecStart=/home/<USER>/xiaohongshu-automation/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 3. 定时任务配置

```bash
# 添加crontab任务
crontab -e

# 每天上午9点执行
0 9 * * * /home/<USER>/xiaohongshu-automation/venv/bin/python /home/<USER>/xiaohongshu-automation/automation-script/main.py

# 每小时检查一次
0 * * * * /home/<USER>/xiaohongshu-automation/scripts/health_check.sh
```

## 📈 监控和维护

### 1. 健康检查

```python
# health_check.py
import requests
import json

def check_service_health():
    try:
        # 检查服务状态
        response = requests.get('http://localhost:8080/health')
        return response.status_code == 200
    except:
        return False

if __name__ == '__main__':
    if check_service_health():
        print("Service is healthy")
    else:
        print("Service is down")
        exit(1)
```

### 2. 性能监控

```python
# monitoring.py
import psutil
import time

def monitor_performance():
    while True:
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        
        print(f"CPU: {cpu_percent}%, Memory: {memory_percent}%")
        
        if cpu_percent > 80 or memory_percent > 80:
            print("Warning: High resource usage")
        
        time.sleep(60)
```

## 🔧 故障排除

### 常见问题及解决方案

1. **ChromeDriver版本不匹配**
   ```bash
   # 自动安装匹配的ChromeDriver
   pip install chromedriver-autoinstaller
   ```

2. **权限问题**
   ```bash
   # Linux/macOS
   sudo chmod +x automation-script/main.py
   
   # Windows
   # 以管理员身份运行
   ```

3. **网络连接问题**
   ```python
   # 增加重试机制
   import requests
   from requests.adapters import HTTPAdapter
   from urllib3.util.retry import Retry
   
   session = requests.Session()
   retry = Retry(total=3, backoff_factor=0.3)
   adapter = HTTPAdapter(max_retries=retry)
   session.mount('http://', adapter)
   session.mount('https://', adapter)
   ```

4. **内存泄漏**
   ```python
   # 定期重启浏览器
   if iteration % 100 == 0:
       browser.close_browser()
       browser.start_browser()
   ```

## 📞 技术支持

如果遇到部署问题，请：

1. 检查系统日志
2. 确认所有依赖已正确安装
3. 验证配置文件格式
4. 查看浏览器控制台错误信息

---

🎉 **部署完成后，您就可以开始使用小红书自动化工具了！**