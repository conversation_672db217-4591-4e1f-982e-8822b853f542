{"browser_profile": "AutomationBot", "headless": false, "delay_between_posts": 10, "max_retries": 3, "screenshots_dir": "screenshots", "excel_file": "notes_data.xlsx", "login_timeout": 60, "xiaohongshu_urls": {"home": "https://www.xiaohongshu.com", "creator_center": "https://creator.xiaohongshu.com", "publish": "https://creator.xiaohongshu.com/publish/publish"}, "deepseek_config": {"api_base": "https://api.deepseek.com", "model": "deepseek-chat", "temperature": 0.7, "max_tokens": 1000}, "selectors": {"title_input": ["//input[contains(@placeholder, '标题')]", "//textarea[contains(@placeholder, '标题')]", "//input[contains(@class, 'title')]"], "content_input": ["//textarea[contains(@placeholder, '内容')]", "//div[contains(@class, 'editor')]//textarea", "//div[contenteditable='true']"], "tag_input": ["//input[contains(@placeholder, '标签')]", "//input[contains(@placeholder, '话题')]"], "save_draft_button": ["//button[contains(text(), '保存草稿')]", "//button[contains(text(), '存草稿')]"], "publish_button": ["//button[contains(text(), '发布')]", "//button[contains(text(), '发布笔记')]"]}}