# 环境变量文件（包含敏感信息）
.env
automation-script/.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
automation-script/venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
automation-script/logs/

# 截图和数据文件
automation-script/screenshots/
automation-script/data/
*.xlsx
*.csv

# 系统文件
.DS_Store
Thumbs.db

# Chrome扩展临时文件
chrome-extension/*.crx
chrome-extension/*.pem

# 测试文件
test_*.py
*_test.py
