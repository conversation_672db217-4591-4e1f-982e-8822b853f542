"""
AI内容改写模块 - DeepSeek API集成
"""
import os
from typing import Dict, List
from openai import OpenAI
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class AIRewriter:
    """AI内容改写类"""
    
    def __init__(self):
        self.api_key = os.getenv('DEEPSEEK_API_KEY')
        self.base_url = os.getenv('DEEPSEEK_API_BASE', 'https://api.deepseek.com')
        
        if not self.api_key:
            raise ValueError("请设置DEEPSEEK_API_KEY环境变量")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def rewrite_note(self, title: str, content: str, tags: List[str]) -> Dict[str, str]:
        """
        改写小红书笔记内容
        
        Args:
            title: 原标题
            content: 原内容
            tags: 原标签列表
            
        Returns:
            Dict: 包含改写后的标题、内容和标签
        """
        try:
            prompt = self._create_rewrite_prompt(title, content, tags)
            
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个专业的内容创作助手，擅长改写小红书笔记内容。请保持原意的基础上，让内容更加生动有趣，符合小红书的风格。"},
                    {"role": "user", "content": prompt}
                ],
                stream=False,
                temperature=0.7,
                max_tokens=1000
            )
            
            result = response.choices[0].message.content
            return self._parse_rewrite_result(result, title, content, tags)
            
        except Exception as e:
            print(f"❌ AI改写失败: {e}")
            return {
                'title': title,
                'content': content,
                'tags': tags
            }
    
    def _create_rewrite_prompt(self, title: str, content: str, tags: List[str]) -> str:
        """创建改写提示词"""
        tags_str = "、".join(tags) if tags else "无"
        
        prompt = f"""请帮我改写以下小红书笔记内容，要求：

1. 保持原意不变，但用不同的表达方式
2. 让内容更加生动有趣，符合小红书风格
3. 标题要吸引人，可以适当使用emoji
4. 内容要自然流畅，不要过于刻意
5. 标签要相关且热门

原内容：
标题：{title}
内容：{content}
标签：{tags_str}

请以JSON格式返回改写结果：
{{
  "title": "改写后的标题",
  "content": "改写后的内容",
  "tags": ["标签1", "标签2", "标签3"]
}}"""
        
        return prompt
    
    def _parse_rewrite_result(self, result: str, original_title: str, original_content: str, original_tags: List[str]) -> Dict[str, str]:
        """解析改写结果"""
        try:
            import json
            
            # 尝试解析JSON格式的结果
            if result.strip().startswith('{'):
                parsed = json.loads(result)
                return {
                    'title': parsed.get('title', original_title),
                    'content': parsed.get('content', original_content),
                    'tags': parsed.get('tags', original_tags)
                }
            else:
                # 如果不是JSON格式，尝试从文本中提取
                lines = result.strip().split('\n')
                
                title = original_title
                content = original_content
                tags = original_tags
                
                current_section = None
                content_lines = []
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                        
                    if '标题' in line or 'title' in line.lower():
                        if '：' in line:
                            title = line.split('：', 1)[1].strip()
                        elif ':' in line:
                            title = line.split(':', 1)[1].strip()
                        current_section = 'title'
                    elif '内容' in line or 'content' in line.lower():
                        current_section = 'content'
                        content_lines = []
                    elif '标签' in line or 'tags' in line.lower():
                        current_section = 'tags'
                        if '：' in line:
                            tags_str = line.split('：', 1)[1].strip()
                            tags = [tag.strip() for tag in tags_str.split('、') if tag.strip()]
                        elif ':' in line:
                            tags_str = line.split(':', 1)[1].strip()
                            tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]
                    else:
                        if current_section == 'content':
                            content_lines.append(line)
                
                if content_lines:
                    content = '\n'.join(content_lines)
                
                return {
                    'title': title,
                    'content': content,
                    'tags': tags
                }
                
        except Exception as e:
            print(f"❌ 解析改写结果失败: {e}")
            return {
                'title': original_title,
                'content': original_content,
                'tags': original_tags
            }
    
    def batch_rewrite(self, notes: List[Dict]) -> List[Dict]:
        """
        批量改写笔记
        
        Args:
            notes: 笔记列表，每个笔记包含title、content、tags
            
        Returns:
            List[Dict]: 改写后的笔记列表
        """
        rewritten_notes = []
        
        for i, note in enumerate(notes, 1):
            print(f"🔄 正在改写第 {i}/{len(notes)} 条笔记...")
            
            try:
                result = self.rewrite_note(
                    title=note.get('title', ''),
                    content=note.get('content', ''),
                    tags=note.get('tags', [])
                )
                
                # 添加改写结果到原笔记中
                note['rewritten_title'] = result['title']
                note['rewritten_content'] = result['content'] 
                note['rewritten_tags'] = result['tags']
                
                rewritten_notes.append(note)
                print(f"✅ 第 {i} 条笔记改写完成")
                
            except Exception as e:
                print(f"❌ 第 {i} 条笔记改写失败: {e}")
                # 保留原内容
                note['rewritten_title'] = note.get('title', '')
                note['rewritten_content'] = note.get('content', '')
                note['rewritten_tags'] = note.get('tags', [])
                rewritten_notes.append(note)
        
        return rewritten_notes
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个测试助手"},
                    {"role": "user", "content": "请回复：连接测试成功"}
                ],
                stream=False,
                max_tokens=50
            )
            
            result = response.choices[0].message.content
            if "连接测试成功" in result:
                print("✅ DeepSeek API连接测试成功")
                return True
            else:
                print("⚠️ API连接异常，返回结果不符合预期")
                return False
                
        except Exception as e:
            print(f"❌ DeepSeek API连接失败: {e}")
            return False


# 使用示例
if __name__ == "__main__":
    # 创建AI改写器实例
    rewriter = AIRewriter()
    
    # 测试连接
    if rewriter.test_connection():
        # 测试改写功能
        test_note = {
            'title': '今天的早餐真好吃',
            'content': '早上起来做了一个三明治，用了火腿、生菜和西红柿，味道很不错。配了一杯牛奶，营养又美味。',
            'tags': ['早餐', '美食', '营养']
        }
        
        result = rewriter.rewrite_note(
            title=test_note['title'],
            content=test_note['content'],
            tags=test_note['tags']
        )
        
        print("\n📝 改写结果:")
        print(f"原标题: {test_note['title']}")
        print(f"新标题: {result['title']}")
        print(f"原内容: {test_note['content']}")
        print(f"新内容: {result['content']}")
        print(f"原标签: {test_note['tags']}")
        print(f"新标签: {result['tags']}")
    else:
        print("❌ 无法连接到DeepSeek API，请检查配置")