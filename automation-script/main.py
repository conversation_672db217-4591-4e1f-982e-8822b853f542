"""
小红书自动化脚本 - 主程序
"""
import os
import json
import time
import pandas as pd
from typing import List, Dict, Optional
from browser_controller import BrowserController
from ai_rewriter import AIRewriter
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class XiaohongshuAutomation:
    """小红书自动化发布类"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config = self.load_config(config_file)
        self.browser = None
        self.ai_rewriter = None
        self.success_count = 0
        self.fail_count = 0
        
        # 初始化AI改写器
        try:
            self.ai_rewriter = AIRewriter()
            print("✅ AI改写器初始化成功")
        except Exception as e:
            print(f"⚠️ AI改写器初始化失败: {e}")
            self.ai_rewriter = None
        
    def load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "browser_profile": "AutomationBot",
            "headless": False,
            "delay_between_posts": 10,
            "max_retries": 3,
            "screenshots_dir": "screenshots",
            "excel_file": "notes_data.xlsx",
            "login_timeout": 60
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    default_config.update(config)
                    print(f"✅ 配置文件已加载: {config_file}")
            except Exception as e:
                print(f"❌ 加载配置文件失败: {e}")
                print("使用默认配置")
        else:
            print("使用默认配置")
            
        return default_config
    
    def save_config(self, config_file: str = "config.json"):
        """保存配置文件"""
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"✅ 配置文件已保存: {config_file}")
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
    
    def load_notes_from_excel(self, excel_file: str) -> List[Dict]:
        """从Excel文件加载笔记数据"""
        try:
            df = pd.read_excel(excel_file)
            notes = []
            
            for _, row in df.iterrows():
                note = {
                    'title': str(row.get('改写标题', '') or row.get('标题', '')),
                    'content': str(row.get('改写内容', '') or row.get('内容', '')),
                    'tags': str(row.get('改写标签', '') or row.get('标签', '')).split(';') if row.get('改写标签') or row.get('标签') else [],
                    'original_title': str(row.get('标题', '')),
                    'original_content': str(row.get('内容', '')),
                    'original_tags': str(row.get('标签', '')).split(';') if row.get('标签') else [],
                    'images': [],  # 图片路径，如果有的话
                    'status': 'pending'
                }
                
                # 清理标签
                note['tags'] = [tag.strip() for tag in note['tags'] if tag.strip()]
                note['original_tags'] = [tag.strip() for tag in note['original_tags'] if tag.strip()]
                
                notes.append(note)
            
            # 如果没有改写内容，尝试使用AI改写
            if self.ai_rewriter and any(not note['title'] or not note['content'] for note in notes):
                print("🤖 检测到部分内容需要AI改写...")
                notes = self.ai_rewrite_notes(notes)
                
            print(f"✅ 成功加载 {len(notes)} 条笔记数据")
            return notes
            
        except Exception as e:
            print(f"❌ 加载Excel文件失败: {e}")
            return []
    
    def ai_rewrite_notes(self, notes: List[Dict]) -> List[Dict]:
        """使用AI改写笔记内容"""
        try:
            for note in notes:
                # 如果改写内容为空，使用AI改写
                if not note['title'] or not note['content']:
                    print(f"🤖 正在AI改写笔记: {note['original_title']}")
                    
                    result = self.ai_rewriter.rewrite_note(
                        title=note['original_title'],
                        content=note['original_content'],
                        tags=note['original_tags']
                    )
                    
                    note['title'] = result['title']
                    note['content'] = result['content']
                    note['tags'] = result['tags']
                    
                    print(f"✅ AI改写完成")
                    time.sleep(1)  # 避免频繁调用API
            
            return notes
            
        except Exception as e:
            print(f"❌ AI改写失败: {e}")
            return notes
    
    def create_screenshots_dir(self):
        """创建截屏目录"""
        screenshots_dir = self.config.get('screenshots_dir', 'screenshots')
        os.makedirs(screenshots_dir, exist_ok=True)
        
    def start_automation(self, excel_file: str = None) -> bool:
        """启动自动化流程"""
        try:
            # 使用配置文件中的Excel文件路径或传入的路径
            excel_file = excel_file or self.config.get('excel_file', 'notes_data.xlsx')
            
            if not os.path.exists(excel_file):
                print(f"❌ Excel文件不存在: {excel_file}")
                return False
            
            # 加载笔记数据
            notes = self.load_notes_from_excel(excel_file)
            if not notes:
                print("❌ 没有可发布的笔记")
                return False
            
            # 创建截屏目录
            self.create_screenshots_dir()
            
            # 启动浏览器
            self.browser = BrowserController(
                profile_name=self.config.get('browser_profile', 'AutomationBot'),
                headless=self.config.get('headless', False)
            )
            
            if not self.browser.start_browser():
                return False
            
            # 导航到创作中心
            if not self.browser.navigate_to_creator_center():
                return False
            
            # 检查登录状态
            if not self.browser.check_login_status():
                print("⏳ 检测到未登录，等待用户登录...")
                if not self.browser.wait_for_login(self.config.get('login_timeout', 60)):
                    print("❌ 用户登录超时")
                    return False
            
            # 批量发布笔记
            self.publish_notes(notes)
            
            # 输出统计信息
            self.print_summary()
            
            return True
            
        except Exception as e:
            print(f"❌ 自动化流程失败: {e}")
            return False
        finally:
            if self.browser:
                self.browser.close_browser()
    
    def publish_notes(self, notes: List[Dict]):
        """批量发布笔记"""
        total_notes = len(notes)
        
        for i, note in enumerate(notes, 1):
            print(f"\n🔄 正在处理第 {i}/{total_notes} 条笔记...")
            print(f"标题: {note['title']}")
            
            success = self.publish_single_note(note, i)
            
            if success:
                self.success_count += 1
                note['status'] = 'success'
                print(f"✅ 第 {i} 条笔记发布成功")
            else:
                self.fail_count += 1
                note['status'] = 'failed'
                print(f"❌ 第 {i} 条笔记发布失败")
            
            # 截屏保存当前状态
            screenshot_path = os.path.join(
                self.config.get('screenshots_dir', 'screenshots'),
                f"note_{i}_{note['status']}.png"
            )
            self.browser.take_screenshot(screenshot_path)
            
            # 延迟一定时间再处理下一条
            if i < total_notes:
                delay = self.config.get('delay_between_posts', 10)
                print(f"⏳ 等待 {delay} 秒后处理下一条笔记...")
                time.sleep(delay)
    
    def publish_single_note(self, note: Dict, index: int) -> bool:
        """发布单条笔记"""
        max_retries = self.config.get('max_retries', 3)
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    print(f"🔄 第 {attempt + 1} 次重试...")
                
                # 创建新笔记
                if not self.browser.create_new_note():
                    continue
                
                # 填写笔记内容
                if not self.browser.fill_note_content(
                    title=note['title'],
                    content=note['content'],
                    tags=note['tags']
                ):
                    continue
                
                # 上传图片（如果有）
                if note.get('images'):
                    self.browser.upload_images(note['images'])
                
                # 保存为草稿
                if not self.browser.save_as_draft():
                    continue
                
                print(f"✅ 笔记已保存为草稿")
                return True
                
            except Exception as e:
                print(f"❌ 发布笔记失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                time.sleep(5)  # 重试前等待5秒
                
        return False
    
    def print_summary(self):
        """打印发布统计信息"""
        print("\n" + "="*50)
        print("📊 发布统计")
        print("="*50)
        print(f"✅ 成功: {self.success_count} 条")
        print(f"❌ 失败: {self.fail_count} 条")
        print(f"📝 总计: {self.success_count + self.fail_count} 条")
        print(f"📈 成功率: {(self.success_count / (self.success_count + self.fail_count) * 100):.1f}%")
        print("="*50)
    
    def update_excel_with_results(self, excel_file: str, notes: List[Dict]):
        """更新Excel文件，添加发布结果"""
        try:
            df = pd.read_excel(excel_file)
            
            # 添加发布状态列
            status_list = [note['status'] for note in notes]
            df['发布状态'] = status_list
            df['发布时间'] = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 保存更新后的Excel文件
            result_file = excel_file.replace('.xlsx', '_发布结果.xlsx')
            df.to_excel(result_file, index=False)
            
            print(f"✅ 发布结果已保存到: {result_file}")
            
        except Exception as e:
            print(f"❌ 更新Excel文件失败: {e}")


def main():
    """主函数"""
    print("🚀 小红书自动化发布工具")
    print("="*50)
    
    # 创建自动化实例
    automation = XiaohongshuAutomation()
    
    # 检查Excel文件
    excel_file = input("请输入Excel文件路径 (默认: notes_data.xlsx): ").strip()
    if not excel_file:
        excel_file = "notes_data.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        print("请确保Excel文件存在并包含以下列:")
        print("- 标题 (或改写标题)")
        print("- 内容 (或改写内容)")
        print("- 标签 (或改写标签)")
        return
    
    # 确认开始
    print(f"\n📋 即将开始自动化发布，Excel文件: {excel_file}")
    print("⚠️  请确保:")
    print("1. Chrome浏览器已安装")
    print("2. 已创建 'AutomationBot' 用户配置文件")
    print("3. 小红书账号已在该配置文件中登录")
    
    confirm = input("\n确认开始吗? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消")
        return
    
    # 开始自动化
    start_time = time.time()
    success = automation.start_automation(excel_file)
    end_time = time.time()
    
    if success:
        print(f"\n🎉 自动化流程完成！耗时: {end_time - start_time:.1f} 秒")
    else:
        print(f"\n❌ 自动化流程失败！耗时: {end_time - start_time:.1f} 秒")


if __name__ == "__main__":
    main()