"""
小红书自动化脚本 - 浏览器控制模块
"""
import time
import json
import os
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import chromedriver_autoinstaller


class BrowserController:
    """浏览器控制类"""
    
    def __init__(self, profile_name: str = "AutomationBot", headless: bool = False):
        self.profile_name = profile_name
        self.headless = headless
        self.driver = None
        self.wait = None
        
        # 自动安装对应版本的ChromeDriver
        chromedriver_autoinstaller.install()
        
    def start_browser(self) -> bool:
        """启动Chrome浏览器"""
        try:
            chrome_options = Options()
            
            # 使用指定的用户配置文件
            chrome_options.add_argument(f"--user-data-dir={os.path.expanduser('~')}/Library/Application Support/Google/Chrome")
            chrome_options.add_argument(f"--profile-directory={self.profile_name}")
            
            # 其他选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if self.headless:
                chrome_options.add_argument("--headless")
                
            # 启动浏览器
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            # 隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print(f"✅ 浏览器已启动，使用配置文件: {self.profile_name}")
            return True
            
        except Exception as e:
            print(f"❌ 启动浏览器失败: {e}")
            return False
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                print("✅ 浏览器已关闭")
            except Exception as e:
                print(f"❌ 关闭浏览器失败: {e}")
                
    def navigate_to_creator_center(self) -> bool:
        """导航到小红书创作中心"""
        try:
            # 先打开小红书首页
            self.driver.get("https://www.xiaohongshu.com")
            time.sleep(3)
            
            # 尝试找到创作中心入口
            creator_center_selectors = [
                "//a[contains(text(), '创作中心')]",
                "//a[contains(@href, 'creator')]",
                "//div[contains(text(), '创作中心')]",
                "//span[contains(text(), '创作中心')]"
            ]
            
            for selector in creator_center_selectors:
                try:
                    element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    element.click()
                    print("✅ 成功点击创作中心入口")
                    time.sleep(3)
                    break
                except TimeoutException:
                    continue
            else:
                # 直接访问创作中心URL
                self.driver.get("https://creator.xiaohongshu.com")
                time.sleep(3)
                
            print("✅ 已导航到创作中心")
            return True
            
        except Exception as e:
            print(f"❌ 导航到创作中心失败: {e}")
            return False
    
    def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            # 检查是否有登录相关元素
            login_indicators = [
                "//div[contains(@class, 'user-info')]",
                "//img[contains(@class, 'avatar')]",
                "//span[contains(text(), '发布笔记')]",
                "//button[contains(text(), '发布')]"
            ]
            
            for selector in login_indicators:
                try:
                    self.driver.find_element(By.XPATH, selector)
                    print("✅ 用户已登录")
                    return True
                except NoSuchElementException:
                    continue
                    
            print("❌ 用户未登录")
            return False
            
        except Exception as e:
            print(f"❌ 检查登录状态失败: {e}")
            return False
    
    def wait_for_login(self, timeout: int = 60) -> bool:
        """等待用户登录"""
        print("⏳ 等待用户登录...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.check_login_status():
                return True
            time.sleep(2)
            
        print("❌ 登录超时")
        return False
    
    def create_new_note(self) -> bool:
        """创建新笔记"""
        try:
            # 查找创建笔记的按钮
            create_note_selectors = [
                "//button[contains(text(), '发布笔记')]",
                "//button[contains(text(), '写笔记')]",
                "//a[contains(text(), '发布笔记')]",
                "//div[contains(text(), '发布笔记')]",
                "//span[contains(text(), '发布笔记')]"
            ]
            
            for selector in create_note_selectors:
                try:
                    element = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    element.click()
                    print("✅ 成功点击创建笔记按钮")
                    time.sleep(3)
                    return True
                except TimeoutException:
                    continue
                    
            print("❌ 找不到创建笔记按钮")
            return False
            
        except Exception as e:
            print(f"❌ 创建新笔记失败: {e}")
            return False
    
    def fill_note_content(self, title: str, content: str, tags: List[str]) -> bool:
        """填写笔记内容"""
        try:
            # 填写标题
            title_selectors = [
                "//input[contains(@placeholder, '标题')]",
                "//textarea[contains(@placeholder, '标题')]",
                "//input[contains(@class, 'title')]",
                "//div[contains(@class, 'title')]//input"
            ]
            
            title_filled = False
            for selector in title_selectors:
                try:
                    title_element = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    title_element.clear()
                    title_element.send_keys(title)
                    print(f"✅ 标题已填写: {title}")
                    title_filled = True
                    break
                except TimeoutException:
                    continue
                    
            if not title_filled:
                print("❌ 标题填写失败")
                return False
            
            time.sleep(1)
            
            # 填写内容
            content_selectors = [
                "//textarea[contains(@placeholder, '内容')]",
                "//div[contains(@class, 'editor')]//textarea",
                "//div[contains(@class, 'content')]//textarea",
                "//div[contenteditable='true']"
            ]
            
            content_filled = False
            for selector in content_selectors:
                try:
                    content_element = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    content_element.clear()
                    content_element.send_keys(content)
                    print(f"✅ 内容已填写: {content[:50]}...")
                    content_filled = True
                    break
                except TimeoutException:
                    continue
                    
            if not content_filled:
                print("❌ 内容填写失败")
                return False
            
            time.sleep(1)
            
            # 添加标签
            if tags:
                tags_text = " ".join([f"#{tag}" for tag in tags])
                
                # 尝试找到标签输入框
                tag_selectors = [
                    "//input[contains(@placeholder, '标签')]",
                    "//input[contains(@placeholder, '话题')]",
                    "//div[contains(@class, 'tag')]//input"
                ]
                
                for selector in tag_selectors:
                    try:
                        tag_element = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                        tag_element.clear()
                        tag_element.send_keys(tags_text)
                        print(f"✅ 标签已添加: {tags_text}")
                        break
                    except TimeoutException:
                        continue
                else:
                    # 如果没有专门的标签输入框，尝试在内容末尾添加标签
                    try:
                        content_element.send_keys(f"\n\n{tags_text}")
                        print(f"✅ 标签已添加到内容末尾: {tags_text}")
                    except:
                        print("❌ 标签添加失败")
            
            print("✅ 笔记内容填写完成")
            return True
            
        except Exception as e:
            print(f"❌ 填写笔记内容失败: {e}")
            return False
    
    def save_as_draft(self) -> bool:
        """保存为草稿"""
        try:
            # 查找保存草稿按钮
            draft_selectors = [
                "//button[contains(text(), '保存草稿')]",
                "//button[contains(text(), '存草稿')]",
                "//span[contains(text(), '保存草稿')]",
                "//div[contains(text(), '保存草稿')]"
            ]
            
            for selector in draft_selectors:
                try:
                    draft_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    draft_button.click()
                    print("✅ 成功点击保存草稿按钮")
                    time.sleep(3)
                    return True
                except TimeoutException:
                    continue
                    
            print("❌ 找不到保存草稿按钮")
            return False
            
        except Exception as e:
            print(f"❌ 保存草稿失败: {e}")
            return False
    
    def upload_images(self, image_paths: List[str]) -> bool:
        """上传图片"""
        try:
            if not image_paths:
                return True
                
            # 查找图片上传按钮
            upload_selectors = [
                "//input[@type='file']",
                "//button[contains(text(), '上传图片')]",
                "//div[contains(@class, 'upload')]//input",
                "//label[contains(@class, 'upload')]//input"
            ]
            
            for selector in upload_selectors:
                try:
                    upload_element = self.driver.find_element(By.XPATH, selector)
                    
                    # 如果是隐藏的input元素，需要先显示
                    if upload_element.tag_name == 'input':
                        self.driver.execute_script("arguments[0].style.display = 'block';", upload_element)
                    
                    # 上传图片
                    for image_path in image_paths:
                        if os.path.exists(image_path):
                            upload_element.send_keys(image_path)
                            time.sleep(2)
                            print(f"✅ 图片已上传: {image_path}")
                    
                    return True
                    
                except NoSuchElementException:
                    continue
                    
            print("❌ 找不到图片上传功能")
            return False
            
        except Exception as e:
            print(f"❌ 上传图片失败: {e}")
            return False
    
    def get_current_url(self) -> str:
        """获取当前页面URL"""
        try:
            return self.driver.current_url
        except:
            return ""
    
    def take_screenshot(self, filename: str) -> bool:
        """截屏保存"""
        try:
            self.driver.save_screenshot(filename)
            print(f"✅ 截屏已保存: {filename}")
            return True
        except Exception as e:
            print(f"❌ 截屏失败: {e}")
            return False