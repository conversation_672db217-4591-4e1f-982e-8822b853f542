# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Xiaohongshu (Little Red Book) content automation tool that consists of two main components:
1. **Chrome Extension** - Content extraction and AI rewriting from Xiaohongshu pages
2. **Python Automation Script** - Automated posting to Xiaohongshu creator center

## Development Commands

### Environment Setup
```bash
# Install dependencies and setup environment
python install.py

# Quick setup (alternative method)
python quick_setup.py

# Check environment health
python check_environment.py

# Test DeepSeek API integration
python test_deepseek.py
```

### Chrome Extension Development
```bash
# Load extension in Chrome
# 1. Open chrome://extensions/
# 2. Enable Developer mode
# 3. Load unpacked extension from chrome-extension/ folder

# Extension files structure:
# - manifest.json: Extension configuration
# - popup.html/js: Extension UI and logic
# - content.js: Page content extraction
# - background.js: Background service worker
```

### Python Script Development
```bash
# Install Python dependencies
cd automation-script
pip install -r requirements.txt

# Run main automation script
python main.py

# Run with specific Excel file
python main.py --excel-file path/to/notes.xlsx
```

### Configuration Management
```bash
# Environment variables are in automation-script/.env
# Main config is in shared/config.json
# Update API keys and browser settings as needed
```

## Architecture Overview

### Core Components

**Chrome Extension Flow:**
- `content.js` extracts note content from Xiaohongshu pages using CSS selectors
- `popup.js` handles UI interactions and calls AI rewriting API
- `background.js` manages data storage and cross-script communication
- Data flows: Page → Content Script → Background → Storage → Export to Excel

**Python Automation Flow:**
- `main.py` orchestrates the entire automation workflow
- `browser_controller.py` handles Selenium WebDriver operations for Chrome automation
- `ai_rewriter.py` integrates with DeepSeek API for content rewriting
- Data flows: Excel Input → AI Rewriting → Browser Automation → Draft Posting

### Key Integration Points

**Cross-Component Data Flow:**
1. Chrome extension extracts and processes content from Xiaohongshu
2. Content gets AI-rewritten using DeepSeek API (model: deepseek-chat)
3. Data exported to Excel with columns: 标题, 内容, 标签, 改写标题, 改写内容, 改写标签
4. Python script reads Excel and automates posting to creator center

**Browser Profile Management:**
- Uses dedicated Chrome profile "AutomationBot" for automation
- Cross-platform Chrome data directory detection in `browser_controller.py`
- Profile isolation ensures automation doesn't interfere with personal browsing

**AI API Integration:**
- Supports DeepSeek API (primary) and OpenAI API (fallback)
- Environment variables: DEEPSEEK_API_KEY, DEEPSEEK_API_BASE
- Prompt engineering for Xiaohongshu content style rewriting

### Configuration System

**Shared Configuration (`shared/config.json`):**
- Browser automation settings (timeouts, delays, retries)
- Xiaohongshu URL endpoints and CSS selectors
- DeepSeek API model parameters
- Robust selector arrays for UI element targeting (handles page structure changes)

**Environment Variables (`.env`):**
- API keys and sensitive configuration
- Debug settings and logging levels
- Never commit real API keys to version control

### Error Handling Strategy

**Chrome Extension:**
- Multiple CSS selector fallbacks for content extraction
- Graceful degradation when AI API fails
- Local storage backup for extracted content

**Python Automation:**
- Retry mechanisms for browser operations
- Screenshot capture for debugging failed operations
- Detailed logging and status reporting
- Automatic ChromeDriver version management

### Security Considerations

- API keys stored in environment variables only
- Chrome profile isolation for automation
- Input validation for Excel data processing
- Rate limiting for AI API calls to prevent quota exhaustion

## Development Notes

When modifying the codebase:

- **Selector Updates**: If Xiaohongshu changes their page structure, update selectors in `shared/config.json`
- **AI Prompt Tuning**: Modify prompts in `ai_rewriter.py` for better content quality
- **Browser Automation**: Test automation scripts with `headless=false` first for debugging
- **Cross-Platform**: All file paths use `pathlib.Path` for Windows/macOS/Linux compatibility

The tool is designed to be resilient to Xiaohongshu's UI changes through configurable selectors and comprehensive error handling.