#!/usr/bin/env python3
"""
小红书自动化工具 - 安装脚本
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path


def print_step(step_num, description):
    """打印安装步骤"""
    print(f"\n{'='*60}")
    print(f"步骤 {step_num}: {description}")
    print('='*60)


def check_python_version():
    """检查Python版本"""
    print_step(1, "检查Python版本")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: Python {version.major}.{version.minor}.{version.micro}")
        sys.exit(1)
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")


def check_chrome_installed():
    """检查Chrome是否安装"""
    print_step(2, "检查Chrome浏览器")
    
    system = platform.system()
    chrome_paths = {
        'Windows': [
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
        ],
        'Darwin': [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        ],
        'Linux': [
            '/usr/bin/google-chrome',
            '/usr/bin/google-chrome-stable',
            '/usr/bin/chromium-browser'
        ]
    }
    
    if system in chrome_paths:
        for path in chrome_paths[system]:
            if os.path.exists(path):
                print(f"✅ 找到Chrome浏览器: {path}")
                return True
    
    print("❌ 未找到Chrome浏览器，请先安装Chrome")
    print("下载地址: https://www.google.com/chrome/")
    return False


def create_virtual_environment():
    """创建虚拟环境"""
    print_step(3, "创建Python虚拟环境")
    
    venv_path = Path("automation-script/venv")
    
    if venv_path.exists():
        print("⚠️ 虚拟环境已存在，跳过创建")
        return True
    
    try:
        subprocess.run([
            sys.executable, "-m", "venv", str(venv_path)
        ], check=True)
        print("✅ 虚拟环境创建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 虚拟环境创建失败: {e}")
        return False


def install_python_dependencies():
    """安装Python依赖"""
    print_step(4, "安装Python依赖包")
    
    system = platform.system()
    if system == "Windows":
        pip_path = "automation-script/venv/Scripts/pip.exe"
    else:
        pip_path = "automation-script/venv/bin/pip"
    
    if not os.path.exists(pip_path):
        print("❌ 找不到pip，请先创建虚拟环境")
        return False
    
    try:
        subprocess.run([
            pip_path, "install", "-r", "automation-script/requirements.txt"
        ], check=True)
        print("✅ Python依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    print_step(5, "创建工作目录")
    
    directories = [
        "automation-script/screenshots",
        "automation-script/logs",
        "automation-script/data",
        "shared/templates"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")


def create_chrome_profile():
    """创建Chrome配置文件"""
    print_step(6, "创建Chrome用户配置文件")
    
    system = platform.system()
    
    if system == "Windows":
        chrome_data_dir = Path.home() / "AppData" / "Local" / "Google" / "Chrome" / "User Data"
    elif system == "Darwin":
        chrome_data_dir = Path.home() / "Library" / "Application Support" / "Google" / "Chrome"
    else:  # Linux
        chrome_data_dir = Path.home() / ".config" / "google-chrome"
    
    profile_dir = chrome_data_dir / "AutomationBot"
    profile_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ Chrome配置文件目录: {profile_dir}")
    print("💡 请在Chrome中手动创建'AutomationBot'用户配置文件")


def create_config_files():
    """创建配置文件"""
    print_step(7, "创建配置文件")
    
    # 创建.env文件
    env_content = """# 小红书自动化工具环境变量
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1

# 小红书账号信息（可选）
XIAOHONGSHU_USERNAME=your_username
XIAOHONGSHU_PASSWORD=your_password

# 其他配置
DEBUG=False
LOG_LEVEL=INFO
"""
    
    with open("automation-script/.env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("✅ 创建环境变量文件: automation-script/.env")
    
    # 创建快捷启动脚本
    if platform.system() == "Windows":
        start_script = """@echo off
cd /d %~dp0
cd automation-script
venv\\Scripts\\activate.bat
python main.py
pause
"""
        with open("start_automation.bat", "w", encoding="utf-8") as f:
            f.write(start_script)
        print("✅ 创建启动脚本: start_automation.bat")
    else:
        start_script = """#!/bin/bash
cd "$(dirname "$0")"
cd automation-script
source venv/bin/activate
python main.py
"""
        with open("start_automation.sh", "w", encoding="utf-8") as f:
            f.write(start_script)
        os.chmod("start_automation.sh", 0o755)
        print("✅ 创建启动脚本: start_automation.sh")


def install_chrome_extension():
    """安装Chrome扩展说明"""
    print_step(8, "安装Chrome扩展")
    
    print("📋 Chrome扩展安装步骤:")
    print("1. 打开Chrome浏览器")
    print("2. 进入扩展程序管理页面: chrome://extensions/")
    print("3. 开启'开发者模式'")
    print("4. 点击'加载已解压的扩展程序'")
    print("5. 选择项目中的'chrome-extension'文件夹")
    print("6. 确认扩展已成功加载")


def run_tests():
    """运行基本测试"""
    print_step(9, "运行基本测试")
    
    try:
        # 测试导入主要模块
        sys.path.insert(0, 'automation-script')
        
        print("🧪 测试导入模块...")
        
        # 测试基本导入
        test_imports = [
            'selenium',
            'pandas',
            'requests',
            'chromedriver_autoinstaller'
        ]
        
        for module in test_imports:
            try:
                __import__(module)
                print(f"✅ {module} 导入成功")
            except ImportError as e:
                print(f"❌ {module} 导入失败: {e}")
                return False
        
        print("✅ 所有模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def print_next_steps():
    """打印后续步骤"""
    print_step(10, "安装完成")
    
    print("🎉 安装完成！")
    print("\n📋 后续步骤:")
    print("1. 配置AI API Key:")
    print("   - 编辑 automation-script/.env 文件")
    print("   - 填入你的OpenAI API Key")
    print("\n2. 创建Chrome用户配置文件:")
    print("   - 打开Chrome浏览器")
    print("   - 创建名为'AutomationBot'的用户配置文件")
    print("   - 在该配置文件中登录小红书账号")
    print("\n3. 安装Chrome扩展:")
    print("   - 按照上述步骤8的说明进行")
    print("\n4. 开始使用:")
    
    if platform.system() == "Windows":
        print("   - 双击 start_automation.bat 启动自动化脚本")
    else:
        print("   - 运行 ./start_automation.sh 启动自动化脚本")
    
    print("\n📖 详细使用说明请查看:")
    print("   - USAGE.md - 使用指南")
    print("   - DEPLOYMENT.md - 部署指南")
    print("   - README.md - 项目说明")


def main():
    """主安装程序"""
    print("🚀 小红书自动化工具安装程序")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    
    # 检查基本要求
    check_python_version()
    
    if not check_chrome_installed():
        print("\n❌ 安装失败：缺少Chrome浏览器")
        sys.exit(1)
    
    # 执行安装步骤
    steps = [
        create_virtual_environment,
        install_python_dependencies,
        create_directories,
        create_chrome_profile,
        create_config_files,
        install_chrome_extension,
        run_tests
    ]
    
    for step in steps:
        if not step():
            print(f"\n❌ 安装失败于步骤: {step.__name__}")
            sys.exit(1)
    
    # 打印完成信息
    print_next_steps()
    
    print("\n" + "="*60)
    print("🎊 安装成功完成！")
    print("="*60)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        sys.exit(1)